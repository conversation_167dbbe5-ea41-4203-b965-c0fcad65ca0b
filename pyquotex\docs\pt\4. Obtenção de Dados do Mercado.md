# Documentação do PyQuotex: Obtenção de Dados do Mercado

## Índice
- [Obtenção de Candlesticks](#obtenção-de-candlesticks)
- [Dados em Tempo Real](#dados-em-tempo-real)
- [Sentimento do Mercado](#sentimento-do-mercado)
- [Preços em Tempo Real](#preços-em-tempo-real)
- [Sinais de Trading](#sinais-de-trading)
- [Lista de Ativos](#lista-de-ativos)
- [Verificação de Ativos](#verificação-de-ativos)

## Obtenção de Candlesticks

### Obter Candlesticks Históricos
```python
async def get_candles(asset, end_from_time, offset, period):
    """
    Obtém candlesticks históricos para um ativo específico.
    
    Parâmetros:
    - asset: str - Nome do ativo (ex. "EURUSD_otc")
    - end_from_time: int - Timestamp de fim
    - offset: int - Deslocamento em segundos (ex. 3600)
    - period: int - Período em segundos (ex. 60)
    """
    candles = await client.get_candles(asset, end_from_time, offset, period)
```

### Obter Candlesticks em Tempo Real
```python
async def get_realtime_candle():
    asset = "EURUSD_otc"
    period = 5  # segundos [60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400]
    candles = await client.get_realtime_candles(asset_name, period)
```

## Dados em Tempo Real

### Iniciar Stream de Dados
```python
def start_candles_stream(asset, period=0):
    client.start_candles_stream(asset, period)
    client.follow_candle(asset)
```

### Parar Stream de Dados
```python
def stop_candles_stream(asset):
    client.unsubscribe_realtime_candle(asset)
    client.unfollow_candle(asset)
```

## Sentimento do Mercado

### Obter Sentimento em Tempo Real
```python
async def get_realtime_sentiment(asset):
    """
    Obtém o sentimento do mercado para um ativo.
    Retorna um dicionário com percentuais de compra/venda.
    """
    sentiment = await client.get_realtime_sentiment(asset_name)
    # Exemplo de resposta: {"sentiment": {"sell": 40, "buy": 60}}
```

## Preços em Tempo Real

### Obter Preços em Tempo Real
```python
async def get_realtime_price():
    asset = "EURJPY_otc"
    await client.start_realtime_price(asset, 60)
    candle_price = await client.get_realtime_price(asset_name)
    # Retorna último preço e timestamp
```

## Sinais de Trading

### Obter Sinais de Trading
```python
async def get_signal_data():
    client.start_signals_data()
    signals = client.get_signal_data()
    # Retorna sinais disponíveis do mercado
```

## Lista de Ativos

### Obter Todos os Ativos
```python
def get_all_asset_name():
    assets = client.get_all_asset_name()
    # Retorna lista de todos os ativos disponíveis
```

### Obter Payouts por Ativo
```python
def get_payment():
    all_data = client.get_payment()
    # Retorna informação de payouts e estado de cada ativo
```

## Verificação de Ativos

### Verificar Disponibilidade de Ativo
```python
async def check_asset_open(asset_name):
    """
    Verifica se um ativo está disponível para operar.
    
    Retorna:
    - Tupla com (ID, nome, estado_abertura)
    - estado_abertura é booleano (True se estiver aberto)
    """
    asset_status = await client.check_asset_open(asset_name)
```

### Obter Ativo com Fallback OTC
```python
async def get_available_asset(asset_name, force_open=True):
    """
    Obtém um ativo e verifica sua disponibilidade.
    Se force_open for True e o ativo estiver fechado, tenta com versão OTC.
    """
    asset_name, asset_data = await client.get_available_asset(asset_name, force_open=True)
```

## Notas de Uso

- Os períodos disponíveis para candlesticks são: 5, 10, 15, 30, 60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400 segundos
- Para ativos OTC, adicionar sufixo "_otc" ao nome do ativo
- É recomendável sempre verificar o estado do ativo antes de operar
- Os streams de dados devem ser parados quando não forem mais necessários para otimizar recursos

## Exemplo de Fluxo Básico

```python
async def basic_market_data_flow():
    # Conectar
    check_connect, message = await client.connect()
    if check_connect:
        # Verificar ativo
        asset = "EURUSD_otc"
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        
        if asset_data[2]:  # Se estiver aberto
            # Iniciar stream
            client.start_candles_stream(asset_name, 60)
            
            # Obter dados
            candles = await client.get_realtime_candles(asset_name, 60)
            sentiment = await client.get_realtime_sentiment(asset_name)
            price = await client.get_realtime_price(asset_name)
            
            # Processar dados...
            
            # Parar stream
            client.stop_candles_stream(asset_name)
    
    await cliente.close()
```