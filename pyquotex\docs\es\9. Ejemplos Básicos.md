# Documentación PyQuotex

Esta documentación cubre los principales casos de uso y ejemplos de implementación de la biblioteca PyQuotex para interactuar con la plataforma Quotex.

## Ejemplos Básicos

### Inicialización y Conexión

```python
from pyquotex.stable_api import Quotex

# Inicializar el cliente
client = Quotex(
    email="<EMAIL>",
    password="tu_password",
    lang="es"  # Idioma por defecto
)


# Conectar al servidor
async def conectar():
    check_connect, message = await client.connect()
    if check_connect:
        print("Conectado exitosamente")
        balance = await client.get_balance()
        print(f"Balance actual: {balance}")
    await cliente.close()
```

### Obtener Información del Perfil

```python
async def obtener_perfil():
    check_connect, message = await client.connect()
    if check_connect:
        profile = await client.get_profile()
        print(f"""
        Usuario: {profile.nick_name}
        Balance Demo: {profile.demo_balance}
        Balance Real: {profile.live_balance}
        País: {profile.country_name}
        """)
    await cliente.close()
```

## Casos de Uso Comunes

### 1. Realizar una Operación Simple

```python
async def operar():
    check_connect, message = await client.connect()
    if check_connect:
        # Parámetros de la operación
        amount = 50  # Cantidad
        asset = "EURUSD_otc"  # Par de divisas
        direction = "call"  # call=subida, put=bajada
        duration = 60  # Duración en segundos
        
        # Verificar si el activo está disponible
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        
        if asset_data[2]:  # Verificar si el mercado está abierto
            status, buy_info = await client.buy(amount, asset_name, direction, duration)
            if status:
                # Esperar resultado
                win = await client.check_win(buy_info["id"])
                profit = client.get_profit()
                print(f"Resultado: {'Ganancia' if win else 'Pérdida'} de {profit}")
    
    await cliente.close()
```

### 2. Monitoreo de Precios en Tiempo Real

```python
async def monitorear_precios():
    check_connect, message = await client.connect()
    if check_connect:
        asset = "EURUSD_otc"
        
        # Iniciar stream de precios
        await client.start_realtime_price(asset, 60)
        
        # Monitorear continuamente
        while True:
            precios = await client.get_realtime_price(asset)
            if precios:
                ultimo_precio = precios[-1]
                print(f"Tiempo: {ultimo_precio['time']} Precio: {ultimo_precio['price']}")
            await asyncio.sleep(1)
```

## Implementaciones Recomendadas

### 1. Gestión de Errores y Reconexión

```python
async def conectar_con_reintentos(max_intentos=5):
    intentos = 0
    while intentos < max_intentos:
        try:
            check_connect, message = await client.connect()
            if check_connect:
                return True
            intentos += 1
            print(f"Reintentando conexión ({intentos}/{max_intentos})")
            await asyncio.sleep(5)
        except Exception as e:
            print(f"Error de conexión: {e}")
            intentos += 1
    return False
```

### 2. Clase Base para Trading

```python
class TradingBot:
    def __init__(self, email, password):
        self.client = Quotex(email=email, password=password)
        self.connected = False
    
    async def iniciar(self):
        self.connected = await self.conectar_con_reintentos()
        if self.connected:
            await self.configurar_cuenta()
    
    async def configurar_cuenta(self):
        # Configurar modo demo por defecto
        self.client.set_account_mode("PRACTICE")
        
    async def cerrar(self):
        await self.cliente.close()
        self.connected = False
```

## Scripts de Ejemplo

### 1. Bot Básico de Trading

```python
import asyncio
from pyquotex.stable_api import Quotex


class SimpleBot:
    def __init__(self):
        self.client = Quotex(
            email="<EMAIL>",
            password="tu_password"
        )
        self.running = False

    async def iniciar(self):
        check_connect, _ = await self.client.connect()
        if check_connect:
            self.running = True
            await self.trading_loop()

    async def trading_loop(self):
        while self.running:
            try:
                # Obtener datos de mercado
                sentiment = await self.client.get_realtime_sentiment("EURUSD_otc")
                if sentiment.get("sentiment", {}).get("buy", 0) > 70:
                    # Ejecutar operación si el sentimiento es muy alcista
                    await self.ejecutar_operacion("EURUSD_otc", "call", 50, 60)

                await asyncio.sleep(60)  # Esperar 1 minuto
            except Exception as e:
                print(f"Error en el loop de trading: {e}")
                await asyncio.sleep(5)

    async def ejecutar_operacion(self, asset, direction, amount, duration):
        status, buy_info = await self.client.buy(amount, asset, direction, duration)
        if status:
            resultado = await self.client.check_win(buy_info["id"])
            print(f"Operación {'ganadora' if resultado else 'perdedora'}")

    async def detener(self):
        self.running = False
        await cliente.close()


# Uso del bot
async def main():
    bot = SimpleBot()
    await bot.iniciar()


if __name__ == "__main__":
    asyncio.run(main())
```

### 2. Monitor de Mercado

```python
import asyncio
import datetime

async def monitor_mercado():
    client = Quotex(
        email="<EMAIL>",
        password="tu_password"
    )
    
    check_connect, _ = await client.connect()
    if check_connect:
        activos = ["EURUSD", "GBPUSD", "USDJPY"]
        
        while True:
            for activo in activos:
                try:
                    # Obtener datos del activo
                    precio = await client.get_realtime_price(activo)
                    sentiment = await client.get_realtime_sentiment(activo)
                    
                    # Guardar o mostrar información
                    timestamp = datetime.datetime.now()
                    print(f"""
                    {timestamp} - {activo}:
                    Precio: {precio[-1]['price'] if precio else 'N/A'}
                    Sentimiento Compra: {sentiment.get('sentiment', {}).get('buy', 'N/A')}%
                    Sentimiento Venta: {sentiment.get('sentiment', {}).get('sell', 'N/A')}%
                    """)
                    
                except Exception as e:
                    print(f"Error monitoreando {activo}: {e}")
                
            await asyncio.sleep(5)  # Actualizar cada 5 segundos

if __name__ == "__main__":
    asyncio.run(monitor_mercado())
```

## Consideraciones Importantes

1. **Gestión de Riesgo**: Siempre implementar controles de riesgo y límites de pérdidas.
2. **Modo Demo**: Probar estrategias primero en modo demo antes de usar dinero real.
3. **Manejo de Errores**: Implementar manejo robusto de errores y reconexiones.
4. **Limitaciones de API**: Considerar las limitaciones y tiempos de espera de la API.

## Notas de Seguridad

- Nunca almacenar credenciales en el código
- Usar variables de entorno o archivos de configuración seguros
- Implementar timeouts y manejo de errores
- Mantener registros de operaciones y errores