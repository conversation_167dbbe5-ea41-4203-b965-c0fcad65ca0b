# Documentação de Conexão e Autenticação - PyQuotex

## Inicialização do Cliente

Para começar a usar o PyQuotex, primeiro você precisa inicializar o cliente com suas credenciais:

```python
from pyquotex.stable_api import Quotex

cliente = Quotex(
    email="<EMAIL>",
    password="sua_senha",
    lang="pt",  # Idioma padrão (pt=Português, en=Inglês, es=Espanhol)
)

# Opcional: Habilitar logs de debugging
cliente.debug_ws_enable = True
```

### Parâmetros de Inicialização Opcionais

- `user_agent`: Personalizar o User-Agent (por padrão usa um predefinido)
- `root_path`: Diretório raiz para armazenar arquivos
- `user_data_dir`: Diretório para dados do usuário
- `asset_default`: Par de moedas padrão (ex: "EURUSD")
- `period_default`: <PERSON><PERSON><PERSON> padr<PERSON> em segundos (ex: 60)

## Processo de Conexão

A conexão é realizada de forma assíncrona. Aqui estão duas formas de estabelecer a conexão:

### 1. Conexão Simples

```python
import asyncio

async def conectar():
    check_connect, mensagem = await cliente.connect()
    if check_connect:
        print("Conexão bem-sucedida!")
        # Seu código aqui
    else:
        print(f"Erro de conexão: {mensagem}")

# Executar
asyncio.run(conectar())
```

### 2. Conexão com Tentativas

```python
async def conectar_com_tentativas(tentativas=5):
    check_connect, mensagem = await cliente.connect()
    
    if not check_connect:
        tentativa = 0
        while tentativa <= tentativas:
            if not await cliente.check_connect():
                check_connect, mensagem = await cliente.connect()
                if check_connect:
                    print("Reconexão bem-sucedida!")
                    break
                else:
                    tentativa += 1
                    print(f"Tentando reconexão {tentativa} de {tentativas}")
            await asyncio.sleep(5)
    
    return check_connect, mensagem
```

## Gerenciamento de Sessões

O PyQuotex gerencia automaticamente as sessões e salva os dados em um arquivo `session.json`. Você pode configurar manualmente os dados da sessão:

```python
cliente.set_session(
    user_agent="Mozilla/5.0...", 
    cookies="seus_cookies",  # Opcional
    ssid="seu_ssid"         # Opcional
)
```

### Definir o tipo de conta ao iniciar

```python
# Definir conta demo
cliente.set_account_mode("PRACTICE")

# Definir conta real
cliente.set_account_mode("REAL")
```

### Mudança de Modo de Conta

```python
# Mudar para conta demo
cliente.change_account("PRACTICE")

# Mudar para conta real
cliente.change_account("REAL")
```

## Reconexão Automática

O sistema implementa uma reconexão automática quando detecta desconexões. No entanto, você também pode gerenciá-la manualmente:

```python
async def manter_conexao():
    while True:
        try:
            if not await cliente.check_connect():
                print("Desconexão detectada, tentando reconectar...")
                check_connect, mensagem = await cliente.connect()
                if check_connect:
                    print("Reconexão bem-sucedida")
                else:
                    print(f"Erro na reconexão: {mensagem}")
                    await asyncio.sleep(5)
            await asyncio.sleep(1)
        except Exception as e:
            print(f"Erro: {e}")
            await asyncio.sleep(5)
```

### Fechamento de Conexão

É importante fechar a conexão quando terminar:

```python
await cliente.close()
```

## Exemplo Completo

```python
import asyncio
from pyquotex.stable_api import Quotex


async def exemplo_completo():
    # Inicialização
    cliente = Quotex(
        email="<EMAIL>",
        password="sua_senha",
        lang="pt"
    )

    try:
        # Conexão com tentativas
        check_connect, mensagem = await conectar_com_tentativas()

        if check_connect:
            # Verificar saldo
            saldo = await cliente.get_balance()
            print(f"Saldo atual: {saldo}")

            # Realizar operações...

    except Exception as e:
        print(f"Erro: {e}")
    finally:
        await cliente.close()


# Executar
asyncio.run(exemplo_completo())
```

## Notas Importantes

1. A biblioteca utiliza WebSocket para manter uma conexão em tempo real.
2. As sessões são armazenadas localmente para evitar logins desnecessários.
3. Todos os métodos que interagem com a API são assíncronos e devem ser tratados com `async/await`.
4. É recomendável implementar tratamento de erros adequado para as reconexões.
5. A autenticação de dois fatores (2FA) é tratada automaticamente se estiver configurada na conta.