# Documentación de Operaciones de Trading PyQuotex

Esta documentación cubre las principales operaciones de trading disponibles en la API de PyQuotex.

## 1. Compra Simple (Buy)

La operación de compra simple permite realizar una transacción básica especificando el monto, activo, dirección y duración.

```python
async def buy_simple():
    amount = 50  # Monto en la moneda de la cuenta
    asset = "AUDCAD"  # Código del activo
    direction = "call"  # Dirección: "call" (arriba) o "put" (abajo)
    duration = 60  # Duración en segundos
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    # status: True/False indicando si la operación fue exitosa
    # buy_info: Información detallada de la orden
```

## 2. Compra con Verificación de Resultado

Esta operación permite realizar una compra y esperar automáticamente el resultado:

```python
async def buy_and_check_win():
    amount = 50
    asset = "EURUSD_otc"
    direction = "call"
    duration = 60
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    if status:
        # Esperar el resultado
        if await client.check_win(buy_info["id"]):
            profit = client.get_profit()
            print(f"Win! Profit: {profit}")
        else:
            loss = client.get_profit()
            print(f"Loss: {loss}")
```

## 3. Compras Múltiples

Permite realizar múltiples operaciones secuenciales:

```python
order_list = [
    {"amount": 5, "asset": "EURUSD", "direction": "call", "duration": 60},
    {"amount": 10, "asset": "AUDCAD_otc", "direction": "put", "duration": 60},
    # ... más órdenes
]

async def buy_multiple(orders=10):
    for i in range(orders):
        order = random.choice(order_list)
        status, buy_info = await client.buy(**order)
```

## 4. Órdenes Pendientes

Las órdenes pendientes permiten programar operaciones para ejecutarse en un momento específico:

```python
async def buy_pending():
    amount = 50
    asset = "AUDCAD"
    direction = "call"
    duration = 60
    open_time = "16/12 15:51"  # Formato: "dd/mm HH:MM"
    
    status, buy_info = await client.open_pending(
        amount, 
        asset_name, 
        direction, 
        duration, 
        open_time
    )
```

## 5. Venta de Opciones

Permite cerrar una posición antes de su vencimiento:

```python
async def sell_option():
    # Primero abrir una posición
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    
    # Luego vender la opción usando su ID
    result = await client.sell_option(buy_info["id"])
```

## 6. Verificación de Resultados

Hay dos formas de verificar resultados:

### 6.1 Verificación directa
```python
async def check_result(operation_id):
    status, operation_info = await client.get_result(operation_id)
    # status: "win" o "loss"
    # operation_info: detalles completos de la operación
```

### 6.2 Verificación en tiempo real
```python
async def check_win(id_number):
    result = await client.check_win(id_number)
    # result: True para ganancia, False para pérdida
```

## 7. Gestión de Balance

### 7.1 Obtener Balance
```python
async def get_balance():
    balance = await client.get_balance()
    print(f"Balance actual: {balance}")
```

### 7.2 Recarga de Balance Demo
```python
async def balance_refill():
    result = await client.edit_practice_balance(5000)
    # Recarga la cuenta demo con 5000
```

### 7.3 Cambio entre Cuenta Demo y Real
```python
# Cambiar a cuenta real
client.set_account_mode("REAL")

# Cambiar a cuenta demo
client.set_account_mode("PRACTICE")
```

## Consideraciones Importantes

1. Todas las operaciones son asíncronas y requieren el uso de `await`
2. Es recomendable verificar que el activo esté disponible antes de operar
3. Los tiempos de expiración son en segundos
4. Las direcciones válidas son "call" (arriba) y "put" (abajo)
5. Siempre verifica el estado de conexión antes de operar
6. Es importante manejar adecuadamente los errores en producción