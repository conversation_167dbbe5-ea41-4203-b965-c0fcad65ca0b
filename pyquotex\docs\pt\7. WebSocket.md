# Documentação WebSocket PyQuotex

## Índice
- [Gerenciamento de WebSocket](#gerenciamento-de-websocket)
- [Inscrição em Streams](#inscrição-em-streams)
- [Streams Disponíveis](#streams-disponíveis)
- [Tratamento de Eventos](#tratamento-de-eventos)
- [Reconexão Automática](#reconexão-automática)

## Gerenciamento de WebSocket

PyQuotex utiliza a biblioteca `websocket-client` para estabelecer e manter conexões WebSocket com o servidor do Quotex. A implementação principal está na classe `WebsocketClient`.

### Inicialização

```python
self.wss = websocket.WebSocketApp(
    self.api.wss_url,
    on_message=self.on_message,
    on_error=self.on_error,
    on_close=self.on_close,
    on_open=self.on_open,
    on_ping=self.on_ping,
    on_pong=self.on_pong,
    header=self.headers
)
```

## Inscrição em Streams

PyQuotex oferece vários métodos para se inscrever em diferentes tipos de streams:

### Inscrição em Candlesticks
```python
def start_candles_stream(self, asset, period=0):
    self.api.current_asset = asset
    self.api.subscribe_realtime_candle(asset, period)
    self.api.follow_candle(asset)
```

### Inscrição em Sentimento do Mercado
```python
async def start_realtime_sentiment(self, asset, period=0):
    self.start_candles_stream(asset, period)
    while True:
        if self.api.realtime_sentiment.get(asset):
            return self.api.realtime_sentiment[asset]
        await asyncio.sleep(0.2)
```

### Inscrição em Preços em Tempo Real
```python
async def start_realtime_price(self, asset, period=0):
    self.start_candles_stream(asset, period)
    while True:
        if self.api.realtime_price.get(asset):
            return self.api.realtime_price
        await asyncio.sleep(0.2)
```

## Streams Disponíveis

O sistema suporta os seguintes tipos de streams:

1. **Candles Stream**
   - Dados de candlesticks em tempo real
   - Histórico de candlesticks
   - Candlesticks em diferentes períodos de tempo

2. **Price Stream**
   - Preços em tempo real
   - Atualizações de preços por ativo

3. **Sentiment Stream**
   - Sentimento do mercado
   - Indicadores de compra/venda

4. **Signals Stream**
   - Sinais de trading
   - Dados de indicadores

## Tratamento de Eventos

### Eventos Principais

1. **on_message**
```python
def on_message(self, wss, message):
    """
    Processa as mensagens recebidas do WebSocket
    - Trata dados de autorização
    - Processa dados de trading
    - Atualiza saldos
    - Trata sinais e sentimento do mercado
    """
```

2. **on_error**
```python
def on_error(self, wss, error):
    """
    Trata erros na conexão WebSocket
    - Registra erros
    - Atualiza estado da conexão
    """
```

3. **on_open**
```python
def on_open(self, wss):
    """
    Inicializa a conexão WebSocket
    - Envia mensagens iniciais
    - Configura streams básicos
    """
```

4. **on_close**
```python
def on_close(self, wss, close_status_code, close_msg):
    """
    Trata o fechamento de conexão
    - Atualiza estado da conexão
    - Prepara para reconexão se necessário
    """
```

## Reconexão Automática

PyQuotex implementa um sistema robusto de reconexão automática:

### Tratamento de Reconexão
```python
async def connect(attempts=5):
    check, reason = await client.connect()
    if not check:
        attempt = 0
        while attempt <= attempts:
            if not await client.check_connect():
                check, reason = await client.connect()
                if check:
                    print("Reconectado com sucesso!")
                    break
                else:
                    print("Erro na reconexão.")
                    attempt += 1
```

### Características de Reconexão

1. **Tentativas Múltiplas**
   - Número configurável de tentativas de reconexão
   - Espera entre tentativas

2. **Estado da Conexão**
   - Monitoramento constante do estado
   - Variáveis globais para tracking
   ```python
   check_websocket_if_connect = None
   check_websocket_if_error = False
   check_rejected_connection = False
   ```

3. **Restauração de Streams**
   - Reinscrição automática em streams ativos
   - Manutenção do estado da aplicação

### Boas Práticas

1. **Tratamento de Erros**
   - Implementar try-catch em operações críticas
   - Logging de erros e eventos importantes

2. **Estado da Conexão**
   - Verificar estado antes de operações
   - Manter tempo de espera adequado entre reconexões

3. **Limpeza de Recursos**
   - Fechar conexões apropriadamente
   - Liberar recursos ao finalizar